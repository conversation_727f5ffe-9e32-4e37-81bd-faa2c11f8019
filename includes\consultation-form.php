<?php
/**
 * Consultation Form Component
 * Reusable consultation form for service pages
 */

// Include database connection
require_once __DIR__ . '/database.php';

// Get services for dropdown
$services = [];
if (isset($database)) {
    $services = $database->getServicesByCategory();
}

// Generate unique form ID for multiple forms on same page
$form_id = 'consultation-form-' . uniqid();
?>

<!-- Consultation Form Section -->
<section id="consultation" class="section-padding bg-gradient-to-r from-primary-600 to-secondary-600">
    <div class="container-custom">
        <div class="max-w-4xl mx-auto">
            <div class="bg-white rounded-2xl shadow-2xl overflow-hidden">
                <div class="grid grid-cols-1 lg:grid-cols-2">
                    <!-- Form Content -->
                    <div class="p-8 lg:p-12">
                        <div class="mb-8">
                            <h2 class="text-3xl font-bold text-gray-900 mb-4">Schedule a Free Consultation</h2>
                            <p class="text-lg text-gray-600">
                                Let's discuss your project requirements and explore how we can help you achieve your goals.
                            </p>
                        </div>
                        
                        <form id="<?php echo $form_id; ?>" class="consultation-form space-y-6" method="POST" action="/consultation-handler">
                            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                                <div>
                                    <label class="form-label" for="<?php echo $form_id; ?>_name">Full Name *</label>
                                    <input type="text" 
                                           id="<?php echo $form_id; ?>_name" 
                                           name="name" 
                                           class="form-input" 
                                           required>
                                </div>
                                <div>
                                    <label class="form-label" for="<?php echo $form_id; ?>_email">Email Address *</label>
                                    <input type="email" 
                                           id="<?php echo $form_id; ?>_email" 
                                           name="email" 
                                           class="form-input" 
                                           required>
                                </div>
                            </div>
                            
                            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                                <div>
                                    <label class="form-label" for="<?php echo $form_id; ?>_organization">Organization</label>
                                    <input type="text" 
                                           id="<?php echo $form_id; ?>_organization" 
                                           name="organization" 
                                           class="form-input">
                                </div>
                                <div>
                                    <label class="form-label" for="<?php echo $form_id; ?>_phone">Phone Number</label>
                                    <input type="tel" 
                                           id="<?php echo $form_id; ?>_phone" 
                                           name="phone" 
                                           class="form-input">
                                </div>
                            </div>
                            
                            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                                <div>
                                    <label class="form-label" for="<?php echo $form_id; ?>_location">Location (City)</label>
                                    <input type="text" 
                                           id="<?php echo $form_id; ?>_location" 
                                           name="location" 
                                           class="form-input" 
                                           placeholder="e.g., New York, NY">
                                </div>
                                <div>
                                    <label class="form-label" for="<?php echo $form_id; ?>_service">Preferred Service</label>
                                    <select id="<?php echo $form_id; ?>_service" 
                                            name="preferred_service" 
                                            class="form-input service-select">
                                        <option value="">Select a service</option>
                                        <?php if (!empty($services)): ?>
                                            <?php foreach ($services as $category => $categoryServices): ?>
                                                <optgroup label="<?php echo htmlspecialchars($category); ?>">
                                                    <?php foreach ($categoryServices as $service): ?>
                                                        <option value="<?php echo htmlspecialchars($service['service_slug']); ?>">
                                                            <?php echo htmlspecialchars($service['service_name']); ?>
                                                        </option>
                                                    <?php endforeach; ?>
                                                </optgroup>
                                            <?php endforeach; ?>
                                        <?php else: ?>
                                            <option value="web-development">Web Development</option>
                                            <option value="mobile-development">Mobile Development</option>
                                            <option value="cloud-services">Cloud Services</option>
                                            <option value="data-analytics">Data Analytics</option>
                                            <option value="cybersecurity">Cybersecurity</option>
                                            <option value="managed-it">Managed IT Services</option>
                                            <option value="other">Other</option>
                                        <?php endif; ?>
                                    </select>
                                </div>
                            </div>
                            
                            <div>
                                <label class="form-label" for="<?php echo $form_id; ?>_description">Project Description/Comments</label>
                                <textarea id="<?php echo $form_id; ?>_description" 
                                          name="description" 
                                          rows="4" 
                                          class="form-textarea" 
                                          placeholder="Tell us about your project requirements, goals, timeline, or any specific questions you have..."></textarea>
                            </div>
                            
                            <div class="flex items-start">
                                <input type="checkbox" 
                                       id="<?php echo $form_id; ?>_consent" 
                                       name="consent" 
                                       class="mt-1 mr-3" 
                                       required>
                                <label for="<?php echo $form_id; ?>_consent" class="text-sm text-gray-600">
                                    I agree to be contacted by Synelogics regarding this consultation request and understand that my information will be handled according to the 
                                    <a href="/privacy-policy" class="text-primary-600 hover:text-primary-700 underline">Privacy Policy</a>. *
                                </label>
                            </div>
                            
                            <div class="pt-4">
                                <button type="submit" class="btn-primary w-full">
                                    <i class="fas fa-calendar-check mr-2"></i>
                                    Schedule Free Consultation
                                </button>
                            </div>
                        </form>
                        
                        <!-- Success/Error Messages -->
                        <div id="<?php echo $form_id; ?>_message" class="mt-6 hidden">
                            <div class="success-message hidden p-4 bg-green-50 border border-green-200 rounded-lg">
                                <div class="flex items-center">
                                    <i class="fas fa-check-circle text-green-600 mr-3"></i>
                                    <div>
                                        <h4 class="text-green-800 font-semibold">Consultation Request Submitted!</h4>
                                        <p class="text-green-700 text-sm mt-1">We'll contact you within 24 hours to schedule your free consultation.</p>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="error-message hidden p-4 bg-red-50 border border-red-200 rounded-lg">
                                <div class="flex items-center">
                                    <i class="fas fa-exclamation-circle text-red-600 mr-3"></i>
                                    <div>
                                        <h4 class="text-red-800 font-semibold">Submission Failed</h4>
                                        <p class="text-red-700 text-sm mt-1">Please try again or contact us directly.</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Info Panel -->
                    <div class="bg-gradient-to-br from-gray-50 to-gray-100 p-8 lg:p-12">
                        <div class="space-y-8">
                            <div>
                                <h3 class="text-xl font-semibold text-gray-900 mb-4">What to Expect</h3>
                                <ul class="space-y-3">
                                    <li class="flex items-start">
                                        <i class="fas fa-clock text-primary-600 mt-1 mr-3"></i>
                                        <div>
                                            <span class="font-medium text-gray-900">30-60 minute consultation</span>
                                            <p class="text-sm text-gray-600">Detailed discussion of your requirements</p>
                                        </div>
                                    </li>
                                    <li class="flex items-start">
                                        <i class="fas fa-user-tie text-primary-600 mt-1 mr-3"></i>
                                        <div>
                                            <span class="font-medium text-gray-900">Expert guidance</span>
                                            <p class="text-sm text-gray-600">Technical recommendations and best practices</p>
                                        </div>
                                    </li>
                                    <li class="flex items-start">
                                        <i class="fas fa-file-alt text-primary-600 mt-1 mr-3"></i>
                                        <div>
                                            <span class="font-medium text-gray-900">Custom proposal</span>
                                            <p class="text-sm text-gray-600">Tailored solution with timeline and pricing</p>
                                        </div>
                                    </li>
                                    <li class="flex items-start">
                                        <i class="fas fa-handshake text-primary-600 mt-1 mr-3"></i>
                                        <div>
                                            <span class="font-medium text-gray-900">No obligation</span>
                                            <p class="text-sm text-gray-600">Free consultation with no strings attached</p>
                                        </div>
                                    </li>
                                </ul>
                            </div>
                            
                            <div class="border-t border-gray-200 pt-8">
                                <h3 class="text-xl font-semibold text-gray-900 mb-4">Contact Information</h3>
                                <div class="space-y-3">
                                    <div class="flex items-center">
                                        <i class="fas fa-phone text-primary-600 mr-3"></i>
                                        <div>
                                            <span class="font-medium text-gray-900">Phone</span>
                                            <p class="text-sm text-gray-600">+****************</p>
                                        </div>
                                    </div>
                                    <div class="flex items-center">
                                        <i class="fas fa-envelope text-primary-600 mr-3"></i>
                                        <div>
                                            <span class="font-medium text-gray-900">Email</span>
                                            <p class="text-sm text-gray-600"><EMAIL></p>
                                        </div>
                                    </div>
                                    <div class="flex items-center">
                                        <i class="fas fa-clock text-primary-600 mr-3"></i>
                                        <div>
                                            <span class="font-medium text-gray-900">Response Time</span>
                                            <p class="text-sm text-gray-600">Within 24 hours</p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="border-t border-gray-200 pt-8">
                                <div class="bg-white rounded-lg p-4 border border-gray-200">
                                    <div class="flex items-center mb-2">
                                        <i class="fas fa-shield-alt text-green-600 mr-2"></i>
                                        <span class="font-medium text-gray-900">Your Privacy is Protected</span>
                                    </div>
                                    <p class="text-xs text-gray-600">
                                        We respect your privacy and will never share your information with third parties. 
                                        All consultations are confidential.
                                    </p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<script>
// Enhanced form handling for consultation form
document.addEventListener('DOMContentLoaded', function() {
    const form = document.getElementById('<?php echo $form_id; ?>');
    const messageContainer = document.getElementById('<?php echo $form_id; ?>_message');
    const successMessage = messageContainer.querySelector('.success-message');
    const errorMessage = messageContainer.querySelector('.error-message');
    
    // Make service select searchable if Select2 is available
    const serviceSelect = document.getElementById('<?php echo $form_id; ?>_service');
    if (typeof $ !== 'undefined' && $.fn.select2) {
        $(serviceSelect).select2({
            placeholder: 'Search for a service...',
            allowClear: true,
            width: '100%'
        });
    }
    
    form.addEventListener('submit', function(e) {
        e.preventDefault();
        
        // Show loading state
        const submitBtn = form.querySelector('button[type="submit"]');
        const originalText = submitBtn.innerHTML;
        submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin mr-2"></i>Submitting...';
        submitBtn.disabled = true;
        
        // Hide previous messages
        messageContainer.classList.add('hidden');
        successMessage.classList.add('hidden');
        errorMessage.classList.add('hidden');
        
        // Prepare form data
        const formData = new FormData(form);
        
        // Submit form via AJAX
        fetch('/consultation-handler.php', {
            method: 'POST',
            body: formData
        })
        .then(response => response.json())
        .then(data => {
            messageContainer.classList.remove('hidden');
            
            if (data.success) {
                successMessage.classList.remove('hidden');
                form.reset();
                
                // Reset Select2 if available
                if (typeof $ !== 'undefined' && $.fn.select2) {
                    $(serviceSelect).val(null).trigger('change');
                }
                
                // Track conversion (if analytics available)
                if (typeof gtag !== 'undefined') {
                    gtag('event', 'consultation_request', {
                        'event_category': 'form_submission',
                        'event_label': 'consultation_form'
                    });
                }
            } else {
                errorMessage.classList.remove('hidden');
                if (data.message) {
                    errorMessage.querySelector('p').textContent = data.message;
                }
            }
        })
        .catch(error => {
            console.error('Form submission error:', error);
            messageContainer.classList.remove('hidden');
            errorMessage.classList.remove('hidden');
        })
        .finally(() => {
            // Restore button state
            submitBtn.innerHTML = originalText;
            submitBtn.disabled = false;
        });
    });
});
</script>
