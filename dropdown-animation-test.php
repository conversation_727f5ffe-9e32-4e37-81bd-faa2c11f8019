<?php
$page_title = "Dropdown Animation Test | Synelogics";
$meta_description = "Testing Services dropdown animation fix";
include 'includes/header.php';
?>

<section class="section-padding bg-white">
    <div class="container-custom">
        <div class="max-w-4xl mx-auto">
            <h1 class="text-4xl font-bold text-gray-900 mb-8">🔧 Services Dropdown Animation Test</h1>
            
            <div class="bg-blue-50 border border-blue-200 rounded-lg p-6 mb-8">
                <h2 class="text-xl font-semibold text-blue-900 mb-4">✅ Animation Fix Applied - V2</h2>
                <p class="text-blue-800 mb-4">
                    The Services dropdown animation has been updated to animate directly from/to the Services button position.
                </p>
                <ul class="text-blue-800 space-y-2">
                    <li>• <strong>Origin Point:</strong> Animation starts from Services button position (not center-right)</li>
                    <li>• <strong>Transform Origin:</strong> Set to <code>15% top</code> to match button location</li>
                    <li>• <strong>Scale Animation:</strong> Uses <code>scale-95</code> to <code>scale-100</code> from button position</li>
                    <li>• <strong>Responsive:</strong> Transform origin adjusts for different screen sizes</li>
                </ul>
            </div>
            
            <div class="bg-yellow-50 border border-yellow-200 rounded-lg p-6 mb-8">
                <h2 class="text-xl font-semibold text-yellow-900 mb-4">🧪 Test Instructions</h2>
                <ol class="text-yellow-800 space-y-2 list-decimal list-inside">
                    <li><strong>Hover Test:</strong> Hover over the "Services" tab in the navigation menu above</li>
                    <li><strong>Check Origin:</strong> Dropdown should appear to grow FROM the Services button position</li>
                    <li><strong>Check Animation:</strong> Should scale up from Services button, not from center-right</li>
                    <li><strong>Exit Test:</strong> Should scale down back TO the Services button position</li>
                    <li><strong>Repeat Test:</strong> Animation should be consistent and smooth every time</li>
                </ol>
            </div>

            <div class="bg-green-50 border border-green-200 rounded-lg p-6 mb-8">
                <h2 class="text-xl font-semibold text-green-900 mb-4">🔧 Technical Implementation</h2>
                <div class="text-green-800 space-y-4">
                    <div>
                        <h3 class="font-semibold">CSS Positioning (includes/header.php):</h3>
                        <pre class="bg-green-100 p-3 rounded text-sm mt-2"><code>.services-dropdown {
    position: absolute;
    left: 50%;
    transform: translateX(-50%);
    transition: opacity 0.3s ease, transform 0.3s ease;
}</code></pre>
                    </div>
                    <div>
                        <h3 class="font-semibold">Alpine.js Integration (includes/nav.php):</h3>
                        <pre class="bg-green-100 p-3 rounded text-sm mt-2"><code>&lt;div x-show="servicesOpen" x-transition class="services-dropdown"&gt;</code></pre>
                    </div>
                    <div>
                        <h3 class="font-semibold">Animation States:</h3>
                        <ul class="text-sm space-y-1 mt-2">
                            <li>• <strong>Hidden:</strong> <code>opacity: 0; transform: translateX(-50%) translateY(-10px);</code></li>
                            <li>• <strong>Visible:</strong> <code>opacity: 1; transform: translateX(-50%) translateY(0);</code></li>
                        </ul>
                    </div>
                </div>
            </div>

            <div class="bg-gray-50 border border-gray-200 rounded-lg p-6">
                <h2 class="text-xl font-semibold text-gray-900 mb-4">📋 Expected Behavior</h2>
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                        <h3 class="font-semibold text-gray-800 mb-2">✅ Correct Behavior:</h3>
                        <ul class="text-gray-700 space-y-1 text-sm">
                            <li>• Dropdown appears centered below Services tab</li>
                            <li>• Smooth fade-in animation (300ms)</li>
                            <li>• Slight upward slide-in motion</li>
                            <li>• No lag or delay</li>
                            <li>• Consistent positioning</li>
                        </ul>
                    </div>
                    <div>
                        <h3 class="font-semibold text-gray-800 mb-2">❌ Fixed Issues:</h3>
                        <ul class="text-gray-700 space-y-1 text-sm">
                            <li>• No more sliding from right side</li>
                            <li>• No more laggy animation</li>
                            <li>• No more positioning jumps</li>
                            <li>• No more transform conflicts</li>
                            <li>• No more inconsistent behavior</li>
                        </ul>
                    </div>
                </div>
            </div>

            <div class="text-center mt-8">
                <a href="<?php echo url(''); ?>" class="btn-primary mr-4">Return to Homepage</a>
                <a href="<?php echo url('services'); ?>" class="btn-outline">View Services</a>
            </div>
        </div>
    </div>
</section>

<script>
// Test dropdown animation programmatically
document.addEventListener('DOMContentLoaded', function() {
    console.log('🔧 Dropdown Animation Test Page Loaded');
    
    // Monitor dropdown behavior
    const servicesButton = document.querySelector('[\\@mouseenter="servicesOpen = true"]');
    const dropdown = document.querySelector('.services-dropdown');
    
    if (servicesButton && dropdown) {
        console.log('✅ Services dropdown elements found');
        
        // Monitor position changes
        let positionTests = 0;
        const maxTests = 3;
        
        function testDropdownPosition() {
            if (positionTests >= maxTests) return;
            
            console.log(`🧪 Testing dropdown position (${positionTests + 1}/${maxTests})...`);
            
            // Trigger dropdown
            servicesButton.dispatchEvent(new Event('mouseenter'));
            
            setTimeout(() => {
                const rect = dropdown.getBoundingClientRect();
                const windowCenter = window.innerWidth / 2;
                const dropdownCenter = rect.left + (rect.width / 2);
                const centerDiff = Math.abs(windowCenter - dropdownCenter);
                
                console.log('Dropdown position analysis:', {
                    windowCenter: windowCenter,
                    dropdownCenter: dropdownCenter,
                    centerDifference: centerDiff,
                    isCentered: centerDiff < 50,
                    dropdownVisible: rect.height > 0
                });
                
                if (centerDiff < 50) {
                    console.log('✅ Dropdown is properly centered');
                } else {
                    console.log('⚠️ Dropdown centering needs adjustment');
                }
                
                // Hide dropdown
                servicesButton.dispatchEvent(new Event('mouseleave'));
                
                positionTests++;
                if (positionTests < maxTests) {
                    setTimeout(testDropdownPosition, 1000);
                }
            }, 400);
        }
        
        // Start testing after page load
        setTimeout(testDropdownPosition, 1000);
    } else {
        console.log('❌ Services dropdown elements not found');
    }
});
</script>

<?php include 'includes/footer.php'; ?>
