<?php
/**
 * Consultation Form Handler for Synelogics Website
 * Processes consultation form submissions with database storage and email notifications
 */

// Set content type for JSON response
header('Content-Type: application/json');

// Enable CORS if needed
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST');
header('Access-Control-Allow-Headers: Content-Type');

// Include required files
require_once __DIR__ . '/includes/database.php';
require_once __DIR__ . '/includes/email-config.php';

// Initialize response array
$response = [
    'success' => false,
    'message' => '',
    'errors' => []
];

// Check if request method is POST
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    $response['message'] = 'Invalid request method. Only POST requests are allowed.';
    echo json_encode($response);
    exit;
}

// Sanitize and validate input data
function sanitizeInput($data) {
    return htmlspecialchars(strip_tags(trim($data)));
}

function validateEmail($email) {
    return filter_var($email, FILTER_VALIDATE_EMAIL);
}

function validatePhone($phone) {
    if (empty($phone)) return true; // Phone is optional
    // Remove all non-numeric characters
    $phone = preg_replace('/[^0-9]/', '', $phone);
    // Check if phone number is between 10-15 digits
    return strlen($phone) >= 10 && strlen($phone) <= 15;
}

// Get and sanitize form data
$name = sanitizeInput($_POST['name'] ?? '');
$email = sanitizeInput($_POST['email'] ?? '');
$organization = sanitizeInput($_POST['organization'] ?? '');
$phone = sanitizeInput($_POST['phone'] ?? '');
$location = sanitizeInput($_POST['location'] ?? '');
$preferredService = sanitizeInput($_POST['preferred_service'] ?? '');
$description = sanitizeInput($_POST['description'] ?? '');
$consent = isset($_POST['consent']) ? true : false;

// Validation
$errors = [];

// Required field validation
if (empty($name)) {
    $errors['name'] = 'Full name is required.';
} elseif (strlen($name) < 2) {
    $errors['name'] = 'Please enter a valid full name.';
}

if (empty($email)) {
    $errors['email'] = 'Email address is required.';
} elseif (!validateEmail($email)) {
    $errors['email'] = 'Please enter a valid email address.';
}

if (!$consent) {
    $errors['consent'] = 'You must agree to be contacted to proceed.';
}

// Optional field validation
if (!empty($phone) && !validatePhone($phone)) {
    $errors['phone'] = 'Please enter a valid phone number.';
}

// Check for validation errors
if (!empty($errors)) {
    $response['errors'] = $errors;
    $response['message'] = 'Please correct the errors below and try again.';
    echo json_encode($response);
    exit;
}

// Prepare data for database insertion
$consultationData = [
    'name' => $name,
    'email' => $email,
    'organization' => $organization,
    'phone' => $phone,
    'location' => $location,
    'preferred_service' => $preferredService,
    'description' => $description
];

try {
    // Save to database
    if (isset($database)) {
        $submissionId = $database->insertConsultationSubmission($consultationData);
    } else {
        throw new Exception('Database connection not available');
    }
    
    // Send email notifications
    $emailSent = sendConsultationEmails($consultationData, $submissionId);
    
    // Prepare success response
    $response['success'] = true;
    $response['message'] = 'Thank you for your consultation request! We will contact you within 24 hours to schedule your free consultation.';
    $response['submission_id'] = $submissionId;
    
    // Log successful submission
    error_log("Consultation submission successful - ID: {$submissionId}, Email: {$email}");
    
} catch (Exception $e) {
    error_log('Consultation submission error: ' . $e->getMessage());
    $response['message'] = 'We apologize, but there was an error processing your request. Please try again or contact us directly.';
}

// Return JSON response
echo json_encode($response);

// Optional: Redirect for non-AJAX submissions
if (!isset($_SERVER['HTTP_X_REQUESTED_WITH']) || strtolower($_SERVER['HTTP_X_REQUESTED_WITH']) !== 'xmlhttprequest') {
    // If this is not an AJAX request, redirect back with success message
    if ($response['success']) {
        header('Location: /contact?consultation_success=1');
    } else {
        header('Location: /contact?consultation_error=1');
    }
    exit;
}

/**
 * Send consultation email notifications
 */
function sendConsultationEmails($data, $submissionId) {
    $emailSent = false;
    
    try {
        // Get service name for display
        $serviceName = getServiceDisplayName($data['preferred_service']);
        
        // Prepare email content
        $currentDate = date('Y-m-d H:i:s');
        
        // Email to sales team
        $salesEmail = getSalesEmail();
        $salesSubject = 'New Consultation Request - Synelogics';
        $salesBody = createSalesEmailBody($data, $submissionId, $serviceName, $currentDate);
        
        // Email to customer (auto-reply)
        $customerSubject = 'Thank you for your consultation request - Synelogics';
        $customerBody = createCustomerEmailBody($data, $serviceName, $currentDate);
        
        // Send emails using configured mailer
        $emailSent = sendEmail($salesEmail, $salesSubject, $salesBody) && 
                    sendEmail($data['email'], $customerSubject, $customerBody);
        
        // Log email status
        if ($emailSent) {
            error_log("Consultation emails sent successfully for submission ID: {$submissionId}");
        } else {
            error_log("Failed to send consultation emails for submission ID: {$submissionId}");
        }
        
    } catch (Exception $e) {
        error_log('Email sending error: ' . $e->getMessage());
    }
    
    return $emailSent;
}

function getServiceDisplayName($serviceSlug) {
    global $database;
    
    if (empty($serviceSlug) || !isset($database)) {
        return 'General Consultation';
    }
    
    try {
        $stmt = $database->getConnection()->prepare("
            SELECT service_name FROM services WHERE service_slug = ? LIMIT 1
        ");
        $stmt->execute([$serviceSlug]);
        $result = $stmt->fetch(PDO::FETCH_ASSOC);
        
        return $result ? $result['service_name'] : ucfirst(str_replace('-', ' ', $serviceSlug));
    } catch (Exception $e) {
        return ucfirst(str_replace('-', ' ', $serviceSlug));
    }
}

function createSalesEmailBody($data, $submissionId, $serviceName, $currentDate) {
    return "
New Consultation Request - Synelogics

Submission ID: {$submissionId}
Date: {$currentDate}

Contact Information:
- Name: {$data['name']}
- Email: {$data['email']}
- Organization: " . ($data['organization'] ?: 'Not provided') . "
- Phone: " . ($data['phone'] ?: 'Not provided') . "
- Location: " . ($data['location'] ?: 'Not provided') . "

Service Interest:
- Preferred Service: {$serviceName}

Project Details:
" . ($data['description'] ?: 'No additional details provided') . "

---
Please follow up within 24 hours to schedule the consultation.

This message was sent from the Synelogics consultation form.
";
}

function createCustomerEmailBody($data, $serviceName, $currentDate) {
    return "
Dear {$data['name']},

Thank you for your interest in Synelogics! We have received your consultation request and appreciate the opportunity to discuss your project.

Here's a summary of your submission:
- Service Interest: {$serviceName}
- Submitted on: {$currentDate}

What happens next:
1. Our team will review your requirements within 24 hours
2. We'll contact you to schedule a convenient time for your free consultation
3. During the consultation, we'll discuss your project in detail and provide expert recommendations
4. You'll receive a custom proposal with timeline and pricing

If you have any urgent questions or need to make changes to your request, please don't hesitate to contact us:

Phone: +****************
Email: <EMAIL>

We look forward to speaking with you soon!

Best regards,
The Synelogics Team

---
Synelogics - Innovative IT Solutions
Website: https://synelogics.com
Email: <EMAIL>
Phone: +****************

This is an automated response. Please do not reply to this email.
";
}
?>
