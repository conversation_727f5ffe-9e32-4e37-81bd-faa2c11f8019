<?php
$page_title = "Carousel Debug | Synelogics";
$meta_description = "Debug carousel functionality";
include 'includes/header.php';
?>

<section class="section-padding bg-white">
    <div class="container-custom">
        <h1 class="text-4xl font-bold text-gray-900 mb-8">🔧 Carousel Debug Page</h1>
        
        <div class="bg-red-50 border border-red-200 rounded-lg p-6 mb-8">
            <h2 class="text-xl font-semibold text-red-900 mb-4">🐛 Debug Information</h2>
            <div id="debug-info" class="text-red-800 font-mono text-sm">
                Loading debug information...
            </div>
        </div>

        <!-- Test Carousel -->
        <div class="industries-carousel" x-data="{ currentSlide: 0, slideWidth: 304, totalSlides: 5, autoAdvanceInterval: null,
            init() {
                console.log('🎠 Debug Carousel Init');
                this.autoAdvanceInterval = setInterval(() => {
                    console.log('🎠 Auto tick, slide:', this.currentSlide);
                    this.nextSlide();
                }, 2000);
            },
            nextSlide() {
                console.log('🎠 Next:', this.currentSlide);
                this.currentSlide++;
                if (this.currentSlide >= this.totalSlides) this.currentSlide = 0;
                console.log('🎠 Now:', this.currentSlide);
            },
            prevSlide() {
                console.log('🎠 Prev:', this.currentSlide);
                this.currentSlide--;
                if (this.currentSlide < 0) this.currentSlide = this.totalSlides - 1;
                console.log('🎠 Now:', this.currentSlide);
            }
        }" id="test-carousel">
            <button class="carousel-nav prev" @click="prevSlide()">
                <i class="fas fa-chevron-left"></i>
            </button>

            <div class="industries-carousel-container" :style="`transform: translateX(-${currentSlide * slideWidth}px)`">
                <div class="industry-card">
                    <div class="w-16 h-16 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-4">
                        <i class="fas fa-heartbeat text-2xl text-red-600"></i>
                    </div>
                    <h4 class="font-semibold text-gray-900 mb-2">Healthcare</h4>
                    <p class="text-sm text-gray-600">Card 1</p>
                </div>

                <div class="industry-card">
                    <div class="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4">
                        <i class="fas fa-university text-2xl text-blue-600"></i>
                    </div>
                    <h4 class="font-semibold text-gray-900 mb-2">Finance</h4>
                    <p class="text-sm text-gray-600">Card 2</p>
                </div>

                <div class="industry-card">
                    <div class="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
                        <i class="fas fa-shopping-cart text-2xl text-green-600"></i>
                    </div>
                    <h4 class="font-semibold text-gray-900 mb-2">E-commerce</h4>
                    <p class="text-sm text-gray-600">Card 3</p>
                </div>

                <div class="industry-card">
                    <div class="w-16 h-16 bg-purple-100 rounded-full flex items-center justify-center mx-auto mb-4">
                        <i class="fas fa-graduation-cap text-2xl text-purple-600"></i>
                    </div>
                    <h4 class="font-semibold text-gray-900 mb-2">Education</h4>
                    <p class="text-sm text-gray-600">Card 4</p>
                </div>

                <div class="industry-card">
                    <div class="w-16 h-16 bg-orange-100 rounded-full flex items-center justify-center mx-auto mb-4">
                        <i class="fas fa-industry text-2xl text-orange-600"></i>
                    </div>
                    <h4 class="font-semibold text-gray-900 mb-2">Manufacturing</h4>
                    <p class="text-sm text-gray-600">Card 5</p>
                </div>
            </div>

            <div class="mt-4 bg-yellow-50 border border-yellow-200 rounded-lg p-4">
                <p class="text-yellow-800 text-sm">
                    <strong>Note:</strong> This debug carousel has 5 cards (vs 10 on homepage) for easier testing.
                    The totalSlides should be adjusted accordingly.
                </p>
            </div>

            <button class="carousel-nav next" @click="nextSlide()">
                <i class="fas fa-chevron-right"></i>
            </button>
        </div>

        <div class="mt-8 grid grid-cols-1 md:grid-cols-2 gap-4">
            <button onclick="testNext()" class="btn-primary">Test Next Slide</button>
            <button onclick="testPrev()" class="btn-outline">Test Prev Slide</button>
        </div>
    </div>
</section>

<script>
let debugCarousel = null;

document.addEventListener('DOMContentLoaded', function() {
    console.log('🔧 Carousel Debug Page Loaded');
    
    // Wait for Alpine.js to initialize
    setTimeout(() => {
        const carouselEl = document.getElementById('test-carousel');
        if (carouselEl && carouselEl._x_dataStack) {
            debugCarousel = carouselEl._x_dataStack[0];
            updateDebugInfo();
            
            // Test auto-advance
            if (debugCarousel.autoAdvanceInterval) {
                console.log('✅ Auto-advance is running');
            } else {
                console.log('❌ Auto-advance is NOT running');
            }
        } else {
            console.log('❌ Alpine.js carousel not found');
        }
    }, 1000);
});

function updateDebugInfo() {
    if (!debugCarousel) return;
    
    const debugInfo = document.getElementById('debug-info');
    debugInfo.innerHTML = `
        <strong>Carousel State:</strong><br>
        Current Slide: ${debugCarousel.currentSlide}<br>
        Total Slides: ${debugCarousel.totalSlides}<br>
        Slides Per View: ${debugCarousel.slidesPerView}<br>
        Slide Width: ${debugCarousel.slideWidth}px<br>
        Max Slides: ${debugCarousel.maxSlides}<br>
        Auto-advance Active: ${debugCarousel.autoAdvanceInterval !== null}<br>
        Transform: translateX(-${debugCarousel.currentSlide * debugCarousel.slideWidth}px)<br>
        Window Width: ${window.innerWidth}px
    `;
}

function testNext() {
    if (debugCarousel) {
        debugCarousel.nextSlide();
        updateDebugInfo();
    }
}

function testPrev() {
    if (debugCarousel) {
        debugCarousel.prevSlide();
        updateDebugInfo();
    }
}

// Update debug info every second
setInterval(updateDebugInfo, 1000);
</script>

<?php include 'includes/footer.php'; ?>
