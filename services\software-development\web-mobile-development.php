<?php
$page_title = "Web & Mobile Development | React, React Native | Synelogics";
$meta_description = "Professional web and mobile app development services using React, Vue.js, React Native, and Flutter for responsive and native applications.";
include '../../includes/header.php';
include '../../includes/image-helper.php';
?>

<!-- Hero Section -->
<section class="hero-gradient py-24">
    <div class="container-custom">
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
            <div class="fade-in-on-scroll">
                <div class="inline-block bg-blue-100 text-blue-600 px-4 py-2 rounded-full text-sm font-medium mb-4">
                    Web & Mobile Development
                </div>
                <h1 class="text-hero text-gradient mb-6">Web & Mobile App Development</h1>
                <p class="text-xl text-gray-600 mb-8 leading-relaxed">
                    Create responsive web applications and native mobile apps that deliver exceptional user experiences across all devices and platforms.
                </p>
                <div class="flex flex-col sm:flex-row gap-4">
                    <a href="#consultation" class="btn-primary text-center">
                        Start Your Project
                    </a>
                    <a href="#portfolio" class="btn-outline text-center">
                        View Portfolio
                    </a>
                </div>
            </div>
            <div class="fade-in-on-scroll">
                <div class="relative">
                    <?php echo createImageTag(
                        getImageUrl('web mobile app development responsive design', 800, 600),
                        'Web & Mobile Development',
                        'rounded-2xl shadow-2xl w-full h-80 lg:h-96 object-cover'
                    ); ?>
                    <div class="absolute -bottom-6 -right-6 bg-white rounded-xl shadow-lg p-4">
                        <div class="flex items-center space-x-3">
                            <div class="w-3 h-3 bg-blue-500 rounded-full animate-pulse"></div>
                            <span class="text-sm font-medium text-gray-700">Cross-Platform</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Services Overview -->
<section class="section-padding bg-white">
    <div class="container-custom">
        <div class="text-center mb-16 fade-in-on-scroll">
            <h2 class="text-section-title text-gray-900 mb-6">What We Build</h2>
            <p class="text-xl text-gray-600 max-w-3xl mx-auto">
                From progressive web apps to native mobile applications, we create digital experiences that engage users and drive business results.
            </p>
        </div>
        
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            <div class="bg-gradient-to-br from-blue-50 to-blue-100 rounded-xl p-8 fade-in-on-scroll">
                <div class="w-16 h-16 bg-blue-600 rounded-xl flex items-center justify-center mb-6">
                    <i class="fas fa-desktop text-2xl text-white"></i>
                </div>
                <h4 class="text-xl font-semibold text-gray-900 mb-4">Progressive Web Apps</h4>
                <p class="text-gray-600 leading-relaxed mb-4">
                    Fast, reliable web applications that work offline and provide native app-like experiences.
                </p>
                <ul class="text-sm text-gray-600 space-y-2">
                    <li>• Offline functionality</li>
                    <li>• Push notifications</li>
                    <li>• App-like interface</li>
                    <li>• Fast loading</li>
                </ul>
            </div>
            
            <div class="bg-gradient-to-br from-green-50 to-green-100 rounded-xl p-8 fade-in-on-scroll">
                <div class="w-16 h-16 bg-green-600 rounded-xl flex items-center justify-center mb-6">
                    <i class="fas fa-mobile-alt text-2xl text-white"></i>
                </div>
                <h4 class="text-xl font-semibold text-gray-900 mb-4">Native Mobile Apps</h4>
                <p class="text-gray-600 leading-relaxed mb-4">
                    High-performance native iOS and Android applications with platform-specific features.
                </p>
                <ul class="text-sm text-gray-600 space-y-2">
                    <li>• iOS & Android native</li>
                    <li>• Platform optimization</li>
                    <li>• Device integration</li>
                    <li>• App store deployment</li>
                </ul>
            </div>
            
            <div class="bg-gradient-to-br from-purple-50 to-purple-100 rounded-xl p-8 fade-in-on-scroll">
                <div class="w-16 h-16 bg-purple-600 rounded-xl flex items-center justify-center mb-6">
                    <i class="fas fa-code text-2xl text-white"></i>
                </div>
                <h4 class="text-xl font-semibold text-gray-900 mb-4">Cross-Platform Solutions</h4>
                <p class="text-gray-600 leading-relaxed mb-4">
                    Single codebase applications that run on multiple platforms with native performance.
                </p>
                <ul class="text-sm text-gray-600 space-y-2">
                    <li>• React Native</li>
                    <li>• Flutter development</li>
                    <li>• Code reusability</li>
                    <li>• Faster deployment</li>
                </ul>
            </div>
        </div>
    </div>
</section>

<!-- Technology Stack -->
<section class="section-padding bg-gray-50">
    <div class="container-custom">
        <div class="text-center mb-16 fade-in-on-scroll">
            <h2 class="text-section-title text-gray-900 mb-6">Our Technology Stack</h2>
            <p class="text-xl text-gray-600 max-w-3xl mx-auto">
                We use modern frameworks and tools to build scalable, maintainable applications.
            </p>
        </div>
        
        <div class="grid grid-cols-1 md:grid-cols-2 gap-12">
            <div class="fade-in-on-scroll">
                <h3 class="text-2xl font-bold text-gray-900 mb-6">Frontend Technologies</h3>
                <div class="grid grid-cols-2 gap-4">
                    <div class="bg-white rounded-lg p-4 text-center shadow-sm">
                        <i class="fab fa-react text-3xl text-blue-600 mb-2"></i>
                        <p class="font-medium text-gray-900">React.js</p>
                    </div>
                    <div class="bg-white rounded-lg p-4 text-center shadow-sm">
                        <i class="fab fa-vuejs text-3xl text-green-600 mb-2"></i>
                        <p class="font-medium text-gray-900">Vue.js</p>
                    </div>
                    <div class="bg-white rounded-lg p-4 text-center shadow-sm">
                        <i class="fab fa-angular text-3xl text-red-600 mb-2"></i>
                        <p class="font-medium text-gray-900">Angular</p>
                    </div>
                    <div class="bg-white rounded-lg p-4 text-center shadow-sm">
                        <i class="fab fa-js-square text-3xl text-yellow-600 mb-2"></i>
                        <p class="font-medium text-gray-900">TypeScript</p>
                    </div>
                </div>
            </div>
            
            <div class="fade-in-on-scroll">
                <h3 class="text-2xl font-bold text-gray-900 mb-6">Mobile Technologies</h3>
                <div class="grid grid-cols-2 gap-4">
                    <div class="bg-white rounded-lg p-4 text-center shadow-sm">
                        <i class="fab fa-react text-3xl text-blue-600 mb-2"></i>
                        <p class="font-medium text-gray-900">React Native</p>
                    </div>
                    <div class="bg-white rounded-lg p-4 text-center shadow-sm">
                        <i class="fas fa-mobile-alt text-3xl text-blue-500 mb-2"></i>
                        <p class="font-medium text-gray-900">Flutter</p>
                    </div>
                    <div class="bg-white rounded-lg p-4 text-center shadow-sm">
                        <i class="fab fa-swift text-3xl text-orange-600 mb-2"></i>
                        <p class="font-medium text-gray-900">Swift</p>
                    </div>
                    <div class="bg-white rounded-lg p-4 text-center shadow-sm">
                        <i class="fab fa-android text-3xl text-green-600 mb-2"></i>
                        <p class="font-medium text-gray-900">Kotlin</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Portfolio Section -->
<section id="portfolio" class="section-padding bg-white">
    <div class="container-custom">
        <div class="text-center mb-16 fade-in-on-scroll">
            <h2 class="text-section-title text-gray-900 mb-6">Recent Projects</h2>
            <p class="text-xl text-gray-600 max-w-3xl mx-auto">
                Explore some of our recent web and mobile development projects.
            </p>
        </div>
        
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            <div class="bg-white rounded-xl shadow-lg overflow-hidden hover:shadow-xl transition-shadow duration-300 fade-in-on-scroll">
                <?php echo createImageTag(
                    getPortfolioImage('ecommerce', 400, 250),
                    'E-commerce Mobile App',
                    'w-full h-48 object-cover'
                ); ?>
                <div class="p-6">
                    <h4 class="text-xl font-semibold text-gray-900 mb-2">E-commerce Mobile App</h4>
                    <p class="text-gray-600 mb-4">React Native app with payment integration and real-time inventory management.</p>
                    <div class="flex flex-wrap gap-2">
                        <span class="px-3 py-1 bg-blue-100 text-blue-600 text-xs rounded-full">React Native</span>
                        <span class="px-3 py-1 bg-green-100 text-green-600 text-xs rounded-full">Node.js</span>
                        <span class="px-3 py-1 bg-purple-100 text-purple-600 text-xs rounded-full">MongoDB</span>
                    </div>
                </div>
            </div>
            
            <div class="bg-white rounded-xl shadow-lg overflow-hidden hover:shadow-xl transition-shadow duration-300 fade-in-on-scroll">
                <?php echo createImageTag(
                    getPortfolioImage('healthcare', 400, 250),
                    'Healthcare Web Platform',
                    'w-full h-48 object-cover'
                ); ?>
                <div class="p-6">
                    <h4 class="text-xl font-semibold text-gray-900 mb-2">Healthcare Web Platform</h4>
                    <p class="text-gray-600 mb-4">HIPAA-compliant patient management system with telemedicine features.</p>
                    <div class="flex flex-wrap gap-2">
                        <span class="px-3 py-1 bg-blue-100 text-blue-600 text-xs rounded-full">React</span>
                        <span class="px-3 py-1 bg-yellow-100 text-yellow-600 text-xs rounded-full">Next.js</span>
                        <span class="px-3 py-1 bg-red-100 text-red-600 text-xs rounded-full">PostgreSQL</span>
                    </div>
                </div>
            </div>
            
            <div class="bg-white rounded-xl shadow-lg overflow-hidden hover:shadow-xl transition-shadow duration-300 fade-in-on-scroll">
                <?php echo createImageTag(
                    getPortfolioImage('fintech', 400, 250),
                    'FinTech Progressive Web App',
                    'w-full h-48 object-cover'
                ); ?>
                <div class="p-6">
                    <h4 class="text-xl font-semibold text-gray-900 mb-2">FinTech Progressive Web App</h4>
                    <p class="text-gray-600 mb-4">Secure financial dashboard with real-time analytics and offline capabilities.</p>
                    <div class="flex flex-wrap gap-2">
                        <span class="px-3 py-1 bg-green-100 text-green-600 text-xs rounded-full">Vue.js</span>
                        <span class="px-3 py-1 bg-blue-100 text-blue-600 text-xs rounded-full">PWA</span>
                        <span class="px-3 py-1 bg-orange-100 text-orange-600 text-xs rounded-full">Firebase</span>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Development Process -->
<section class="section-padding bg-gray-50">
    <div class="container-custom">
        <div class="text-center mb-16 fade-in-on-scroll">
            <h2 class="text-section-title text-gray-900 mb-6">Our Development Process</h2>
            <p class="text-xl text-gray-600 max-w-3xl mx-auto">
                We follow a proven development methodology to ensure quality, timely delivery, and client satisfaction.
            </p>
        </div>
        
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            <div class="text-center fade-in-on-scroll">
                <div class="w-20 h-20 bg-blue-600 rounded-full flex items-center justify-center mx-auto mb-6">
                    <span class="text-2xl font-bold text-white">1</span>
                </div>
                <h4 class="text-lg font-semibold text-gray-900 mb-4">Discovery & Planning</h4>
                <p class="text-gray-600 text-sm">Requirements gathering, user research, and technical architecture planning.</p>
            </div>
            
            <div class="text-center fade-in-on-scroll">
                <div class="w-20 h-20 bg-green-600 rounded-full flex items-center justify-center mx-auto mb-6">
                    <span class="text-2xl font-bold text-white">2</span>
                </div>
                <h4 class="text-lg font-semibold text-gray-900 mb-4">Design & Prototyping</h4>
                <p class="text-gray-600 text-sm">UI/UX design, wireframing, and interactive prototypes for validation.</p>
            </div>
            
            <div class="text-center fade-in-on-scroll">
                <div class="w-20 h-20 bg-purple-600 rounded-full flex items-center justify-center mx-auto mb-6">
                    <span class="text-2xl font-bold text-white">3</span>
                </div>
                <h4 class="text-lg font-semibold text-gray-900 mb-4">Development & Testing</h4>
                <p class="text-gray-600 text-sm">Agile development with continuous testing and quality assurance.</p>
            </div>
            
            <div class="text-center fade-in-on-scroll">
                <div class="w-20 h-20 bg-orange-600 rounded-full flex items-center justify-center mx-auto mb-6">
                    <span class="text-2xl font-bold text-white">4</span>
                </div>
                <h4 class="text-lg font-semibold text-gray-900 mb-4">Launch & Support</h4>
                <p class="text-gray-600 text-sm">Deployment, monitoring, and ongoing maintenance and support.</p>
            </div>
        </div>
    </div>
</section>

<!-- Consultation Form -->
<?php include '../../includes/consultation-form.php'; ?>

<?php include '../../includes/footer.php'; ?>
