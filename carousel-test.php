<?php
$page_title = "Carousel Functionality Test | Synelogics";
$meta_description = "Testing carousel dot indicators and functionality";
include 'includes/header.php';
?>

<section class="section-padding bg-white">
    <div class="container-custom">
        <div class="max-w-4xl mx-auto">
            <h1 class="text-4xl font-bold text-gray-900 mb-8">🎠 Carousel Functionality Test</h1>
            
            <div class="bg-green-50 border border-green-200 rounded-lg p-6 mb-8">
                <h2 class="text-xl font-semibold text-green-900 mb-4">✅ Carousel Fixes Applied</h2>
                <p class="text-green-800 mb-4">
                    Both carousel dot indicator issues have been fixed on the homepage.
                </p>
                <ul class="text-green-800 space-y-2">
                    <li>• <strong>Industries Carousel:</strong> Added missing Alpine.js properties (totalDots, maxSlides)</li>
                    <li>• <strong>Testimonials Carousel:</strong> Added 2 additional slides to match the 3 dot indicators</li>
                    <li>• <strong>Dot Indicators:</strong> Now properly update to show active/current slide</li>
                    <li>• <strong>Auto-advance:</strong> Testimonials auto-advance every 5 seconds</li>
                </ul>
            </div>
            
            <div class="bg-blue-50 border border-blue-200 rounded-lg p-6 mb-8">
                <h2 class="text-xl font-semibold text-blue-900 mb-4">🧪 Test Instructions</h2>
                <ol class="text-blue-800 space-y-2 list-decimal list-inside">
                    <li><strong>Visit Homepage:</strong> Go to <a href="<?php echo url(''); ?>" class="underline">homepage</a></li>
                    <li><strong>Industries Carousel:</strong> Scroll to "Industries We Serve" section</li>
                    <li><strong>Test Navigation:</strong> Click left/right arrows and dot indicators</li>
                    <li><strong>Testimonials Carousel:</strong> Scroll to "What Our Clients Say" section</li>
                    <li><strong>Test Auto-advance:</strong> Wait 5 seconds to see automatic slide changes</li>
                    <li><strong>Test Dot Clicks:</strong> Click different dots to jump to specific slides</li>
                </ol>
            </div>

            <div class="bg-yellow-50 border border-yellow-200 rounded-lg p-6 mb-8">
                <h2 class="text-xl font-semibold text-yellow-900 mb-4">🔧 Technical Implementation</h2>
                <div class="text-yellow-800 space-y-4">
                    <div>
                        <h3 class="font-semibold">Industries Carousel (Alpine.js):</h3>
                        <pre class="bg-yellow-100 p-3 rounded text-sm mt-2"><code>// Added missing computed properties
get maxSlides() {
    return Math.max(0, this.totalSlides - this.slidesPerView);
},
get totalDots() {
    return Math.ceil(this.totalSlides / this.slidesPerView);
}</code></pre>
                    </div>
                    <div>
                        <h3 class="font-semibold">Testimonials Carousel (JavaScript):</h3>
                        <pre class="bg-yellow-100 p-3 rounded text-sm mt-2"><code>// Added 2 additional slides with new testimonials
// Auto-advance every 5 seconds
// Dot click navigation functionality</code></pre>
                    </div>
                </div>
            </div>

            <div class="bg-gray-50 border border-gray-200 rounded-lg p-6">
                <h2 class="text-xl font-semibold text-gray-900 mb-4">📋 Expected Behavior</h2>
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                        <h3 class="font-semibold text-gray-800 mb-2">Industries Carousel:</h3>
                        <ul class="text-gray-700 space-y-1 text-sm">
                            <li>• Dots update when using arrow navigation</li>
                            <li>• Clicking dots jumps to correct slide group</li>
                            <li>• Active dot is highlighted in blue</li>
                            <li>• Responsive behavior on different screen sizes</li>
                            <li>• Smooth transitions between slides</li>
                        </ul>
                    </div>
                    <div>
                        <h3 class="font-semibold text-gray-800 mb-2">Testimonials Carousel:</h3>
                        <ul class="text-gray-700 space-y-1 text-sm">
                            <li>• Auto-advances every 5 seconds</li>
                            <li>• 3 slides with 3 testimonials each</li>
                            <li>• Dots show current slide (blue = active)</li>
                            <li>• Clicking dots jumps to specific slide</li>
                            <li>• Smooth fade transitions between slides</li>
                        </ul>
                    </div>
                </div>
            </div>

            <div class="text-center mt-8">
                <a href="<?php echo url(''); ?>" class="btn-primary mr-4">Test on Homepage</a>
                <a href="<?php echo url('fixes-verification'); ?>" class="btn-outline">View All Fixes</a>
            </div>
        </div>
    </div>
</section>

<script>
// Test carousel functionality
document.addEventListener('DOMContentLoaded', function() {
    console.log('🎠 Carousel Test Page Loaded');
    
    // Test if Alpine.js industriesCarousel function is available
    if (typeof industriesCarousel === 'function') {
        console.log('✅ industriesCarousel function is available');
        
        // Test the function
        const testCarousel = industriesCarousel();
        console.log('Industries Carousel Test:', {
            totalSlides: testCarousel.totalSlides,
            slidesPerView: testCarousel.slidesPerView,
            totalDots: testCarousel.totalDots,
            maxSlides: testCarousel.maxSlides
        });
    } else {
        console.log('❌ industriesCarousel function not found');
    }
    
    // Test testimonial slider elements
    const testimonialSlider = document.querySelector('.testimonial-slider');
    if (testimonialSlider) {
        const slides = testimonialSlider.querySelectorAll('.testimonial-slide');
        const dots = testimonialSlider.querySelectorAll('.slider-dot');
        
        console.log('Testimonials Carousel Test:', {
            totalSlides: slides.length,
            totalDots: dots.length,
            slidesMatch: slides.length === dots.length
        });
        
        if (slides.length === dots.length) {
            console.log('✅ Testimonials carousel setup is correct');
        } else {
            console.log('⚠️ Testimonials carousel slides/dots mismatch');
        }
    } else {
        console.log('❌ Testimonial slider not found on this page');
    }
});
</script>

<?php include 'includes/footer.php'; ?>
